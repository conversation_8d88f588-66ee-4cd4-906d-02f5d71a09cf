// components/ui/Button.tsx

import React from 'react';
import {
    ActivityIndicator,
    StyleSheet,
    Text,
    TextStyle,
    ViewStyle
} from 'react-native';

import { BorderRadius, FontSizes, FontWeights, Spacing } from '@/constants/Colors';
import { useTheme } from '@/hooks/useTheme';

// Material Design 3 Button Variants
export type ButtonVariant = 'filled' | 'filledTonal' | 'outlined' | 'text' | 'elevated';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

function Button({
  title,
  onPress,
  variant = 'filled',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
  iconPosition = 'left',
}: ButtonProps) {
  const { colors } = useTheme();

  const getButtonStyles = () => {
    const baseStyle: ViewStyle = {
      borderRadius: BorderRadius.xl,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      minHeight: 40,
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle.paddingHorizontal = Spacing.lg;
        baseStyle.paddingVertical = Spacing.sm;
        baseStyle.minHeight = 32;
        break;
      case 'large':
        baseStyle.paddingHorizontal = Spacing.xxl;
        baseStyle.paddingVertical = Spacing.lg;
        baseStyle.minHeight = 48;
        break;
      default: // medium
        baseStyle.paddingHorizontal = Spacing.xl;
        baseStyle.paddingVertical = Spacing.md;
        baseStyle.minHeight = 40;
    }

    // Material Design 3 variant styles
    switch (variant) {
      case 'filled':
        baseStyle.backgroundColor = disabled ? colors.surfaceVariant : colors.primary;
        break;
      case 'filledTonal':
        baseStyle.backgroundColor = disabled ? colors.surfaceVariant : colors.secondaryContainer;
        break;
      case 'outlined':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = disabled ? colors.outlineVariant : colors.outline;
        break;
      case 'text':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.paddingHorizontal = Spacing.lg;
        break;
      case 'elevated':
        baseStyle.backgroundColor = disabled ? colors.surfaceVariant : colors.surface;
        baseStyle.elevation = 1;
        baseStyle.shadowColor = colors.onSurface;
        baseStyle.shadowOffset = { width: 0, height: 1 };
        baseStyle.shadowOpacity = 0.05;
        baseStyle.shadowRadius = 2;
        break;
    }

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    return baseStyle;
  };

  const getTextStyles = () => {
    const baseStyle: TextStyle = {
      fontWeight: FontWeights.medium,
      fontSize: FontSizes.labelLarge,
      textAlign: 'center',
    };

    // Material Design 3 text colors
    switch (variant) {
      case 'filled':
        baseStyle.color = disabled ? colors.onSurfaceVariant : colors.onPrimary;
        break;
      case 'filledTonal':
        baseStyle.color = disabled ? colors.onSurfaceVariant : colors.onSecondaryContainer;
        break;
      case 'outlined':
      case 'text':
        baseStyle.color = disabled ? colors.onSurfaceVariant : colors.primary;
        break;
      case 'elevated':
        baseStyle.color = disabled ? colors.onSurfaceVariant : colors.primary;
        break;
    }

    return baseStyle;
  };

  const styles = StyleSheet.create({
    button: getButtonStyles(),
    text: getTextStyles(),
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    iconContainer: {
      marginRight: iconPosition === 'left' ? Spacing.sm : 0,
      marginLeft: iconPosition === 'right' ? Spacing.sm : 0,
    },
    loading: {
      marginRight: Spacing.sm,
    },
  });

  const renderContent = () => {
    if (loading) {
      return (
        <>
          <ActivityIndicator
            size="small"
            color={styles.text.color}
            style={styles.loading}
          />
          <Text style={[styles.text, textStyle]}>Loading...</Text>
        </>
      );
    }

    return (
      <>
        {icon && iconPosition === 'left' && (
          <View style={styles.iconContainer}>
            {React.cloneElement(icon as React.ReactElement, {
              color: styles.text.color,
            })}
          </View>
        )}
        <Text style={[styles.text, textStyle]}>{title}</Text>
        {icon && iconPosition === 'right' && (
          <View style={styles.iconContainer}>
            {React.cloneElement(icon as React.ReactElement, {
              color: styles.text.color,
            })}
          </View>
        )}
      </>
    );
  };

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {renderContent()}
      </View>
    </TouchableOpacity>
  );
}

export default Button;
